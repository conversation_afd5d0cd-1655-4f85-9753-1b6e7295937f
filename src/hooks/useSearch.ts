import { useState, useCallback, useMemo } from 'react';

/**
 * Configuration for the useSearch hook
 */
export interface UseSearchConfig<T> {
  /** Initial data items */
  initialItems?: T[];
  /** Fields to search within */
  searchFields: (keyof T)[];
  /** Minimum search length before filtering */
  minSearchLength?: number;
  /** Whether search is case sensitive */
  caseSensitive?: boolean;
}

/**
 * Return type for the useSearch hook
 */
export interface UseSearchReturn<T> {
  /** Current search term */
  searchTerm: string;
  /** Set the search term */
  setSearchTerm: (term: string) => void;
  /** Filtered results based on current search term */
  filteredItems: T[];
  /** Set the items to search through */
  setItems: (items: T[]) => void;
  /** Current items being searched */
  items: T[];
  /** Clear the search term */
  clearSearch: () => void;
  /** Whether search is currently active (term length >= minSearchLength) */
  isSearchActive: boolean;
}

/**
 * Custom hook for managing search state and filtering logic
 * 
 * This hook provides a convenient way to manage search functionality
 * alongside the SearchBar component, handling the filtering logic
 * and state management.
 * 
 * @example
 * ```tsx
 * function UserList() {
 *   const {
 *     searchTerm,
 *     setSearchTerm,
 *     filteredItems,
 *     setItems,
 *     isSearchActive
 *   } = useSearch<User>({
 *     searchFields: ['name', 'email', 'phone'],
 *     minSearchLength: 2
 *   });
 * 
 *   useEffect(() => {
 *     setItems(users);
 *   }, [users, setItems]);
 * 
 *   return (
 *     <div>
 *       <SearchBar<User>
 *         config={{
 *           searchFields: ['name', 'email', 'phone'],
 *           minSearchLength: 2
 *         }}
 *         value={searchTerm}
 *         onChange={setSearchTerm}
 *         items={items}
 *         onResults={() => {}} // Results handled by hook
 *       />
 *       
 *       {filteredItems.map(user => (
 *         <div key={user.id}>{user.name}</div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
export function useSearch<T>({
  initialItems = [],
  searchFields,
  minSearchLength = 2,
  caseSensitive = false
}: UseSearchConfig<T>): UseSearchReturn<T> {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [items, setItems] = useState<T[]>(initialItems);

  // Determine if search is currently active
  const isSearchActive = searchTerm.length >= minSearchLength;

  // Memoized filtered results
  const filteredItems = useMemo(() => {
    if (!isSearchActive) {
      return items;
    }

    const searchLower = caseSensitive ? searchTerm : searchTerm.toLowerCase();
    
    return items.filter((item) => {
      return searchFields.some((field) => {
        const fieldValue = item[field];
        if (fieldValue == null) return false;
        
        const stringValue = String(fieldValue);
        const compareValue = caseSensitive ? stringValue : stringValue.toLowerCase();
        
        // Handle array fields (like flags, tags)
        if (Array.isArray(fieldValue)) {
          return fieldValue.some(arrayItem => {
            const arrayStringValue = String(arrayItem);
            const arrayCompareValue = caseSensitive ? arrayStringValue : arrayStringValue.toLowerCase();
            return arrayCompareValue.includes(searchLower);
          });
        }
        
        return compareValue.includes(searchLower);
      });
    });
  }, [items, searchTerm, searchFields, isSearchActive, caseSensitive]);

  // Clear search function
  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    filteredItems,
    setItems,
    items,
    clearSearch,
    isSearchActive
  };
}

/**
 * Hook for API-based search functionality
 */
export interface UseApiSearchConfig<T> {
  /** Function to perform the search */
  searchFunction: (searchTerm: string) => Promise<T[]>;
  /** Minimum search length before triggering API call */
  minSearchLength?: number;
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
}

export interface UseApiSearchReturn<T> {
  /** Current search term */
  searchTerm: string;
  /** Set the search term */
  setSearchTerm: (term: string) => void;
  /** Search results from API */
  results: T[];
  /** Loading state */
  loading: boolean;
  /** Error state */
  error: string | null;
  /** Clear search and results */
  clearSearch: () => void;
  /** Manually trigger search */
  triggerSearch: (term?: string) => Promise<void>;
}

/**
 * Custom hook for API-based search functionality
 * 
 * @example
 * ```tsx
 * function ApiUserSearch() {
 *   const {
 *     searchTerm,
 *     setSearchTerm,
 *     results,
 *     loading,
 *     error,
 *     clearSearch
 *   } = useApiSearch<User>({
 *     searchFunction: async (term) => {
 *       const response = await fetch(`/api/users/search?q=${term}`);
 *       return response.json();
 *     },
 *     minSearchLength: 3,
 *     debounceDelay: 500
 *   });
 * 
 *   return (
 *     <div>
 *       <SearchBar<User>
 *         config={{
 *           searchFields: ['name', 'email'],
 *           minSearchLength: 3,
 *           debounceDelay: 500
 *         }}
 *         value={searchTerm}
 *         onChange={setSearchTerm}
 *         loading={loading}
 *         error={error}
 *       />
 *       
 *       {results.map(user => (
 *         <div key={user.id}>{user.name}</div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
export function useApiSearch<T>({
  searchFunction,
  minSearchLength = 2,
  debounceDelay = 300
}: UseApiSearchConfig<T>): UseApiSearchReturn<T> {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [results, setResults] = useState<T[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Trigger search function
  const triggerSearch = useCallback(async (term?: string) => {
    const searchValue = term ?? searchTerm;

    if (searchValue.length < minSearchLength) {
      setResults([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const searchResults = await searchFunction(searchValue);
      setResults(searchResults);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, [searchFunction, searchTerm, minSearchLength]);

  // Clear search and results
  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setResults([]);
    setError(null);
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    results,
    loading,
    error,
    clearSearch,
    triggerSearch
  };
}

/**
 * Configuration for combined search and filter functionality
 */
export interface UseSearchAndFilterConfig<T> {
  /** Initial data items */
  initialItems?: T[];
  /** Search configuration */
  searchConfig: {
    searchFields: (keyof T)[];
    minSearchLength?: number;
    caseSensitive?: boolean;
  };
  /** Filter configuration */
  filterConfig?: {
    filterProperty: keyof T;
    allowMultiSelect?: boolean;
  };
}

/**
 * Return type for the useSearchAndFilter hook
 */
export interface UseSearchAndFilterReturn<T> {
  /** Current search term */
  searchTerm: string;
  /** Set the search term */
  setSearchTerm: (term: string) => void;
  /** Current active filters */
  activeFilters: string[];
  /** Set active filters */
  setActiveFilters: (filters: string[]) => void;
  /** Final filtered results (after both search and filter) */
  filteredItems: T[];
  /** Items after search filtering only */
  searchFilteredItems: T[];
  /** Items after property filtering only */
  propertyFilteredItems: T[];
  /** Set the items to search/filter through */
  setItems: (items: T[]) => void;
  /** Current items being processed */
  items: T[];
  /** Clear search term */
  clearSearch: () => void;
  /** Clear all filters */
  clearFilters: () => void;
  /** Clear both search and filters */
  clearAll: () => void;
  /** Whether search is currently active */
  isSearchActive: boolean;
  /** Whether filters are currently active */
  isFilterActive: boolean;
}

/**
 * Combined hook for search and filter functionality
 *
 * This hook manages both search and filter state, applying them in sequence:
 * 1. First applies search filtering
 * 2. Then applies property-based filtering to search results
 *
 * @example
 * ```tsx
 * function WorkOrderList() {
 *   const {
 *     searchTerm,
 *     setSearchTerm,
 *     activeFilters,
 *     setActiveFilters,
 *     filteredItems,
 *     searchFilteredItems,
 *     setItems,
 *     clearAll
 *   } = useSearchAndFilter<WorkOrder>({
 *     searchConfig: {
 *       searchFields: ['description', 'location'],
 *       minSearchLength: 2
 *     },
 *     filterConfig: {
 *       filterProperty: 'status',
 *       allowMultiSelect: true
 *     }
 *   });
 *
 *   useEffect(() => {
 *     setItems(workOrders);
 *   }, [workOrders, setItems]);
 *
 *   return (
 *     <div>
 *       <SearchBar<WorkOrder>
 *         config={searchConfig}
 *         value={searchTerm}
 *         onChange={setSearchTerm}
 *         items={items}
 *         onResults={() => {}} // Handled by hook
 *       />
 *
 *       <FilterBar<WorkOrder>
 *         config={filterConfig}
 *         items={searchFilteredItems}
 *         activeFilters={activeFilters}
 *         onActiveFiltersChange={setActiveFilters}
 *         onFilterChange={() => {}} // Handled by hook
 *       />
 *
 *       {filteredItems.map(item => (
 *         <div key={item.id}>{item.description}</div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
export function useSearchAndFilter<T>({
  initialItems = [],
  searchConfig,
  filterConfig
}: UseSearchAndFilterConfig<T>): UseSearchAndFilterReturn<T> {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [items, setItems] = useState<T[]>(initialItems);

  const {
    searchFields,
    minSearchLength = 2,
    caseSensitive = false
  } = searchConfig;

  // Determine if search/filters are active
  const isSearchActive = searchTerm.length >= minSearchLength;
  const isFilterActive = activeFilters.length > 0;

  // Apply search filtering first
  const searchFilteredItems = useMemo(() => {
    if (!isSearchActive) {
      return items;
    }

    const searchLower = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    return items.filter((item) => {
      return searchFields.some((field) => {
        const fieldValue = item[field];
        if (fieldValue == null) return false;

        const stringValue = String(fieldValue);
        const compareValue = caseSensitive ? stringValue : stringValue.toLowerCase();

        // Handle array fields
        if (Array.isArray(fieldValue)) {
          return fieldValue.some(arrayItem => {
            const arrayStringValue = String(arrayItem);
            const arrayCompareValue = caseSensitive ? arrayStringValue : arrayStringValue.toLowerCase();
            return arrayCompareValue.includes(searchLower);
          });
        }

        return compareValue.includes(searchLower);
      });
    });
  }, [items, searchTerm, searchFields, isSearchActive, caseSensitive]);

  // Apply property filtering to search results
  const filteredItems = useMemo(() => {
    if (!filterConfig || !isFilterActive) {
      return searchFilteredItems;
    }

    const { filterProperty } = filterConfig;

    return searchFilteredItems.filter((item) => {
      const propertyValue = item[filterProperty];

      if (propertyValue == null) return false;

      // Handle array properties
      if (Array.isArray(propertyValue)) {
        return activeFilters.some(filter =>
          propertyValue.some(arrayItem => String(arrayItem) === filter)
        );
      } else {
        // Handle single values
        return activeFilters.includes(String(propertyValue));
      }
    });
  }, [searchFilteredItems, activeFilters, filterConfig, isFilterActive]);

  // Apply only property filtering (for FilterBar items prop)
  const propertyFilteredItems = useMemo(() => {
    if (!filterConfig || !isFilterActive) {
      return items;
    }

    const { filterProperty } = filterConfig;

    return items.filter((item) => {
      const propertyValue = item[filterProperty];

      if (propertyValue == null) return false;

      if (Array.isArray(propertyValue)) {
        return activeFilters.some(filter =>
          propertyValue.some(arrayItem => String(arrayItem) === filter)
        );
      } else {
        return activeFilters.includes(String(propertyValue));
      }
    });
  }, [items, activeFilters, filterConfig, isFilterActive]);

  // Clear functions
  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  const clearFilters = useCallback(() => {
    setActiveFilters([]);
  }, []);

  const clearAll = useCallback(() => {
    setSearchTerm('');
    setActiveFilters([]);
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    activeFilters,
    setActiveFilters,
    filteredItems,
    searchFilteredItems,
    propertyFilteredItems,
    setItems,
    items,
    clearSearch,
    clearFilters,
    clearAll,
    isSearchActive,
    isFilterActive
  };
}
